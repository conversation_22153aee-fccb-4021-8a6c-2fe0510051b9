/* Transaction List Component Styles */

.content-blur {
  filter: blur(2px);
  pointer-events: none;
}

/* Page Header */
.page-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

/* Search Container */
.search-container {
  position: relative;
}

.search-container .form-control {
  padding-left: 2.5rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--card-bg-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.search-container .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color), 0.25);
  background: var(--card-bg-color);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  z-index: 10;
}

/* Member Avatar */
.member-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Member Card */
.member-card {
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.member-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Member Card Header Layout */
.member-card .modern-card-header {
  padding: 1.25rem;
}

.member-card .modern-card-header .row {
  margin: 0;
  width: 100%;
}

.member-card .modern-card-header .row > div {
  padding: 0 0.5rem;
}

/* Member Info Section */
.member-card h6 {
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.member-card .text-muted {
  color: var(--text-muted) !important;
}

/* Debt Info Styling */
.member-card .text-danger {
  color: var(--danger-color) !important;
  font-weight: 700;
}

/* Button Styling in Member Card */
.member-card .btn-success {
  background: var(--success-color);
  border-color: var(--success-color);
  font-weight: 600;
  transition: all 0.3s ease;
}

.member-card .btn-success:hover {
  background: var(--success-hover-color);
  border-color: var(--success-hover-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--success-color), 0.3);
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
}

.empty-state i {
  opacity: 0.5;
}

/* Transaction Group */
.member-transaction-group {
  margin-bottom: 2rem;
}

/* Button Group Toggle */
.btn-group .btn {
  border-radius: 0;
  border-color: var(--border-color);
  background: var(--card-bg-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.btn-group .btn:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.btn-group .btn:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.btn-group .btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(var(--primary-color), 0.3);
}

.btn-group .btn:hover:not(.active) {
  background: var(--hover-bg-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-group .btn .badge {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
}

.btn-group .btn:not(.active) .badge {
  background: var(--secondary-color);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .member-avatar {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }

  .modern-stats-card {
    margin-bottom: 1rem;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 8px !important;
    margin-bottom: 0.5rem;
  }

  .btn-group .btn:last-child {
    margin-bottom: 0;
  }

  /* Member Card Mobile Layout */
  .member-card .modern-card-header .row {
    margin: 0;
  }

  .member-card .modern-card-header .row > div {
    padding: 0.5rem 0;
  }

  /* Mobile'da orta kısım gizlenir, sadece sol ve sağ görünür */
  .member-card .modern-card-header .col-lg-4.col-md-3 {
    display: none;
  }

  /* Sağ taraftaki buton ve badge'i alt satıra al */
  .member-card .modern-card-header .col-lg-4.col-md-4.col-12 {
    margin-top: 0.5rem;
  }

  .member-card .modern-card-header .col-lg-4.col-md-4.col-12 .d-flex {
    justify-content: space-between;
  }
}

/* Tablet Layout */
@media (max-width: 992px) and (min-width: 769px) {
  .member-card .modern-card-header .col-lg-4.col-md-3 {
    text-align: left;
  }

  .member-card .modern-card-header .col-lg-4.col-md-3 .d-flex {
    justify-content: flex-start;
  }
}

@media (max-width: 576px) {
  .modern-stats-card .modern-stats-value {
    font-size: 1.5rem;
  }

  .modern-stats-card .modern-stats-label {
    font-size: 0.75rem;
  }

  .modern-table {
    font-size: 0.8rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.5rem 0.25rem;
  }

  .modern-badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
  }

  /* Çok küçük ekranlarda buton yazısını kısalt */
  .btn-sm .fas.fa-money-bill-wave + span {
    display: none;
  }

  .btn-sm::after {
    content: "Öde";
  }
}

/* Dark Theme Support */
[data-theme="dark"] .search-container .form-control {
  background: var(--card-bg-color);
  border-color: var(--border-color);
  color: var(--text-color);
}

[data-theme="dark"] .search-container .form-control:focus {
  border-color: var(--primary-color);
  background: var(--card-bg-color);
}

[data-theme="dark"] .member-card {
  background: var(--card-bg-color);
  border-color: var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .btn-group .btn {
  background: var(--card-bg-color);
  border-color: var(--border-color);
  color: var(--text-color);
}

[data-theme="dark"] .btn-group .btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

[data-theme="dark"] .btn-group .btn:hover:not(.active) {
  background: var(--hover-bg-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

